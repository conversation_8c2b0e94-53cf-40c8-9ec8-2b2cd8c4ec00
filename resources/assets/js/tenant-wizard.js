$(document).ready(function () {

    let sectionContent = $('#section-content');
    let documentPreview = $('#document-preview');
    let formContainer = $('#form-container');
    let errorContainer = $('#javascript-error-container');
    let form = formContainer.find('form');

    let isMobileDevice = window.innerWidth < 992;
    let sectionFormChanged = false;

    // on document form submit
    $('.submit-link').on('click', function (e) {

        // if validation exists and fails, stop execution
        if(!form[0].reportValidity()) {
            e.preventDefault();
            return;
        }
        else if (typeof window.validate == 'function' && !window.validate()) {
            e.preventDefault();
            return;
        }

        processSectionFormSubmit();
        form.submit();
    });

    function processSectionFormSubmit(ajax = false, isLivePreview = false) {
        if (!ajax) {
            // remove all hidden inputs (except token and parties)
            form.find(":hidden").not('option').not($("input[name='_token']")).not($("input[name='parties']")).remove();
        }

        // append document parties information
        if (window["appendParties"]) {
            window["appendParties"]();
        }

        // append missing data field
        if (!isLivePreview) {
            let missingData = getMissingRequiredData();
            if (missingData.length) {
                let missingDataNames = missingData.map(function () {
                    return $(this).attr('name');
                }).get();
                let missingDataJson = JSON.stringify(missingDataNames);
                form.append('<input type="hidden" name="missing_data" value=\'' + missingDataJson + '\'/>');
            }
        }

    }

    // on focus radio input text, select radio
    $('.radio_input_text').on('focus', function () {
        $(this).closest('.form-check').find('.form-check-input').click();
    });

    // on focus radio input text, select radio
    $('.checkbox_input_text').on('focus', function () {
        $(this).closest('.form-check').find('.form-check-input').prop('checked', true);
    });

    function getDataForAjax(isLivePreview = false) {
        processSectionFormSubmit(true, isLivePreview);

        let data = []; // Initialize an array to hold our name=value pairs

        let addedRadios = {};

        // Loop through all form elements
        form.find(':input').each(function() {
            let element = $(this);
            let name = element.attr('name');
            let type = element.attr('type');
            let value = element.val();

            // Skip elements without names or certain types
            if (!name || type === 'submit' || type === 'button') {
                return;
            }

            // Handle different input types
            if (type === 'checkbox') {
                if (element.is(':checked')) {
                    data.push(name + '=' + encodeURIComponent(value));
                }
            } else if (type === 'radio') {
                if (element.is(':checked')) {
                    // Only add if we haven't already added this radio group
                    if (!addedRadios[name]) {
                        data.push(name + '=' + encodeURIComponent(value));
                        addedRadios[name] = true;
                    }
                }
            } else if (element.is('select')) {
                if (element.prop('multiple')) {
                    // Handle multiple select
                    element.find('option:selected').each(function() {
                        data.push(name + '=' + encodeURIComponent($(this).val()));
                    });
                } else {
                    data.push(name + '=' + encodeURIComponent(value));
                }
            } else {
                // Handle text, textarea, hidden, etc.
                data.push(name + '=' + encodeURIComponent(value));
            }
        });

        return data.join('&');
    }

    function getMissingRequiredData() {
        return form.find(':input[required]').filter(function() {
            let element = $(this);
            let type = element.attr('type');
            
            if (type === 'checkbox' || type === 'radio') {
                return !element.is(':checked');
            } else {
                return !element.val();
            }
        });
    }

    // preview link click
    $('.preview-link').on('click', function (e) {
        e.preventDefault();

        let data = getDataForAjax();
        let url = form.attr('action');
        let href = $(this).attr('data-route');

        // prevent browser from blocking the window
        let windowReference = window.open();

        $.ajax({
            type: "POST",
            url: url,
            data: data, // serializes the form's elements.
            success: function (data, status) {

                if(windowReference) {
                    windowReference.location = href;
                }
                // some browsers do not allow popups
                else {
                    window.location.href = href;
                }

            },
            error: function (data) {
                if(windowReference) {
                    windowReference.close();
                }
                alert("Došlo je do pogreške. Molimo pokušajte ponovo.");
            },
        });
    });

    // update document preview
    function updatePreview() {
        if (documentPreview.length && !isMobileDevice) {
            
            let data = getDataForAjax(true);
            let url = documentPreview.data('url');
            
            // Add document and section IDs
            data += '&document_id=' + encodeURIComponent($('#form-container').data('document-id') || '');
            data += '&section_id=' + encodeURIComponent($('#form-container').data('section-id') || '');

            $.ajax({
                type: "POST",
                url: url,
                data: data,
                success: function (response) {
                    documentPreview.html(response);
                },
                error: function () {
                    console.log('Preview update failed');
                }
            });
        }
    }

    // debounced version of updatePreview
    let debouncedUpdatePreview = debounce(updatePreview, 500);

    // bind events for updating document preview
    if (documentPreview.length && !isMobileDevice) {
        formContainer
            .on('click', 'a', updatePreview)
            .on('keyup', 'input[type="text"], input[type="number"], textarea', debouncedUpdatePreview)
            .on('change', 'input[type="text"], input[type="number"], textarea', updatePreview)
            .on('change', 'input[type="checkbox"], input[type="radio"], select', updatePreview);

        $(document).on('updatePreviewEvent', updatePreview)
            .on('mapsAutoFill', updatePreview);
    }

    // track form changes
    formContainer.on('input change', ':input', function() {
        sectionFormChanged = true;
    });

    // on unsaved changes and .nav-link click, ask user to confirm
    $(document).on('click', '.nav-link', function(e) {
        if(sectionFormChanged) {
            e.preventDefault();
            // save intended page
            $('#intended-exit-page').val($(this).attr('href'));
            $('#saveChangesModal').modal('show');
        }
    })

    // if user wants to continue without saving, redirect to intended page
    $('#continue-without-saving').click(function (e) {
        e.preventDefault();
        window.location = $('#intended-exit-page').val();
    });

    // if user wants to save and continue
    $('#save-and-continue').click(function (e) {
        e.preventDefault();

        // get data
        let data = getDataForAjax();
        let url = form.attr('action');

        $.ajax({
            type: "POST",
            url: url,
            data: data, // serializes the form's elements.
            success: function (data, status) {
                // redirect to intended page
                window.location = $('#intended-exit-page').val();
            },
            error: function () {
                alert("Došlo je do pogreške. Molimo pokušajte ponovo.");
            },
        });
    });

    // show section content after page load
    sectionContent.show();

    // utility function for debouncing
    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            let context = this, args = arguments;
            let later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            let callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    function showWizardTutorialModal() {

        let introModal = $('#wizardTutorialModal');

        if(!isMobileDevice && introModal.length) {
            introModal.modal('show');

            $.ajax({
                url: '/user/wizard/tutorial',
                type: 'POST',
                data: {
                    _token: $("[name='_token']").val(),
                }
            });
        }
    }

    showWizardTutorialModal();

});
