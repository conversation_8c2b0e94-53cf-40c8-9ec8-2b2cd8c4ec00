/* Tenant Portal CSS */

/* Prevent layout shifts from scrollbar */
html {
    overflow-y: scroll;
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #c1c1c1 #f1f1f1; /* For Firefox */
}

/* Custom scrollbar for Chrome, Safari, and Edge */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Base styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
    /* Prevent layout shifts from scrollbar in Chrome/Safari/Edge */
    margin-right: calc(100vw - 100%);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2d3748;
}

.display-4 {
    font-weight: 700;
}

.lead {
    font-weight: 400;
    font-size: 1.15rem;
}

/* Navbar */
.navbar {
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: #4A6FDC !important;
}

.nav-link {
    font-weight: 500;
    color: #4a5568 !important;
    padding: 0.5rem 1.5rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #4A6FDC !important;
}

.navbar-spacing .nav-item {
    margin: 0 0.1rem;
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #4A6FDC;
    border-color: #4A6FDC;
}

.btn-primary:hover {
    background-color: #3a5cbe;
    border-color: #3a5cbe;
}

.btn-outline-primary {
    color: #4A6FDC;
    border-color: #4A6FDC;
}

.btn-outline-primary:hover {
    background-color: #4A6FDC;
    border-color: #4A6FDC;
}

/* Cards */
.card {
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    font-weight: 600;
    padding: 1rem 1.5rem;
    background-color: #4A6FDC;
    color: white;
    border-bottom: none;
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4A6FDC;
    box-shadow: 0 0 0 0.2rem rgba(74, 111, 220, 0.25);
}

.form-label {
    font-weight: 500;
    color: #4a5568;
}

/* Alerts */
.alert {
    border-radius: 0.375rem;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Footer */
.footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    color: #6c757d;
}

/* Jumbotron */
.jumbotron {
    background-color: #4A6FDC;
    border-radius: 0.5rem;
    padding: 3rem 2rem;
}

/* Feature icons */
.feature-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #edf2ff;
    margin-bottom: 1.5rem;
}

/* Social login buttons */
.social-login {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1.25rem;
    }
}

/* Dashboard specific styles */
.dashboard-card {
    border-left: 4px solid #4A6FDC;
}

.dashboard-icon {
    font-size: 2rem;
    color: #4A6FDC;
}

/* Custom background colors */
.bg-primary {
    background-color: #4A6FDC !important;
}

.bg-secondary {
    background-color: #718096 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

/* Text colors */
.text-primary {
    color: #4A6FDC !important;
}

/* Links */
a {
    color: #4A6FDC;
    transition: all 0.3s ease;
}

a:hover {
    color: #3a5cbe;
    text-decoration: none;
}

/* Document search container */
.document-search-container {
    position: relative;
}

.document-search-container .select2-container {
    width: 100% !important;
}

.document-search-container .select2-selection--single {
    height: 48px !important;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
}

.document-search-container .select2-selection__rendered {
    line-height: 30px !important;
    padding-left: 0;
    color: #333;
}

.document-search-container .select2-selection__placeholder {
    color: #9c9c9c;
}

.document-search-container .select2-selection__arrow {
    height: 46px !important;
    right: 10px;
}

.document-search-container .select2-dropdown {
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.document-search-container .select2-results__option {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.document-search-container .select2-results__option--highlighted {
    background-color: #4A6FDC !important;
    color: white;
}

/* Ensure search input is visible and functional */
.document-search-container .select2-search--dropdown {
    padding: 0.5rem;
}

.document-search-container .select2-search__field {
    width: 100% !important;
    padding: 0.5rem !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 0.25rem !important;
    font-size: 14px !important;
}

.document-search-container .select2-search__field:focus {
    outline: none !important;
    border-color: #4A6FDC !important;
    box-shadow: 0 0 0 0.2rem rgba(74, 111, 220, 0.25) !important;
}

/* Fix for dropdown positioning */
.document-search-container .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.document-search-container .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* Shadow utilities */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Dropdown menu */
.dropdown-menu {
    border-radius: 0.375rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #edf2ff;
    color: #4A6FDC;
}

/* Custom spacing */
.mt-6 {
    margin-top: 4rem;
}

.mb-6 {
    margin-bottom: 4rem;
}

/* Custom border radius */
.rounded-lg {
    border-radius: 0.5rem !important;
}

/* Custom transitions */
.transition {
    transition: all 0.3s ease;
}
