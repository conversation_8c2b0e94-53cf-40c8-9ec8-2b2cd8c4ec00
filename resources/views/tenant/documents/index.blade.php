@extends('layouts.tenant.master')

@section('title', 'Dokumenti')

@section('content')
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h2 class="mb-4">Dokumenti</h2>

                    <!-- Document Creation Form -->
                    <div class="row">
                        <div class="col-lg-12">
                            <h5 class="mb-3">Izradi novi dokument</h5>
                            {{ html()->form('POST', route('tenant.document.create'))->open() }}
                            @csrf
                            <div class="form-row">
                                <div class="col-12 col-md-9 mb-2 mb-md-0">
                                    <div class="document-search-container">
                                        <select name="template_id" id="tenant-select-document" class="form-control form-control-lg" required>
                                            <option value="">Odaberi obrazac...</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3">
                                    {{ html()->button('Izradi')->type('submit')->class('btn btn-primary btn-lg btn-block') }}
                                </div>
                            </div>
                            {{ html()->form()->close() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Moji dokumenti</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-striped" id="templates-table">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Dokument</th>
                                <th>Stvoren</th>
                                <th>Ažuriran</th>
                                <th>Akcije</th>
                            </tr>
                            </thead>
                            <tbody>
                            <!-- Table content -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {
            // Initialize Select2 for tenant document search
            if ($('#tenant-select-document').length) {
                $('#tenant-select-document').select2({
                    placeholder: 'Odaberi obrazac...',
                    search: true,
                    allowClear: false,
                    minimumInputLength: 0,
                    language: {
                        errorLoading: function () {
                            return 'Nije pronađen nijedan dokument.';
                        },
                        loadingMore: function () {
                            return 'Pretražujem dokumente...';
                        },
                        maximumSelected: function (args) {
                            return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                        },
                        noResults: function () {
                            return 'Nema rezultata';
                        },
                        searching: function () {
                            return 'Pretražujem dokumente...';
                        }
                    },
                    escapeMarkup: function (markup) {
                        return markup; // Allows rendering of HTML markup
                    },
                    ajax: {
                        url: '{{ route('tenant.search.templates') }}',
                        type: 'post',
                        dataType: 'json',
                        delay: 0,
                        data: function (params) {
                            return {
                                _token: $("[name='_token']").val(), // csrf token
                                query: params.term || '', // search term (empty string for initial load)
                            };
                        },
                        processResults: function (data) {
                            let results = [];

                            if(data['query']){
                                // When there's a search query, show flat results
                                $.each(data['results'], ($_i, _data) => {
                                    results.push({
                                        'id': _data['id'],
                                        'text': _data['title'],
                                    });
                                });
                            }
                            else{
                                // When no search query, show categorized results
                                $.each(data['results'], (_category, _data) => {

                                    let templates = [];

                                    $.each(_data['templates'], (__order_index, __template_data) => {
                                        templates.push({
                                            'id': __template_data['id'],
                                            'text': __template_data['title'],
                                        });
                                    });

                                    results.push({
                                        'text': _data['category'],
                                        'children': templates
                                    });
                                });
                            }

                            return {
                                results: results
                            };
                        },
                        cache: true
                    }
                });
            }
        });
    </script>
@endpush
