<!DOCTYPE html>
<html lang="hr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name') }} - @yield('title', 'Dobrodošli')</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/select2.min.css" rel="stylesheet" />
    <link href="{{ mix('css/tenant.css') }}" rel="stylesheet">

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js"></script>
    <script src="/js/select2.min.js"></script>
    <script src="{{ mix('js/app.js') }}"></script>

    @stack('styles')
</head>
<body>
<div id="app">
    <nav class="navbar navbar-expand-md navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('tenant.home') }}">
                {{ config('app.name') }}
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <!-- Left Side Of Navbar -->
                <ul class="navbar-nav mr-auto">

                </ul>

                <!-- Right Side Of Navbar -->
                <ul class="navbar-nav ml-auto navbar-spacing">
                    <!-- Authentication Links -->
                    @guest
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('tenant.login') }}">{{ __('Prijava') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('tenant.register') }}">{{ __('Registracija') }}</a>
                        </li>
                    @else
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('tenant.documents') }}">{{ __('Moji dokumenti') }}</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                {{ Auth::user()->name }}
                            </a>

                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                <a class="dropdown-item" href="{{ route('tenant.logout') }}"
                                   onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                    {{ __('Odjava') }}
                                </a>

                                <form id="logout-form" action="{{ route('tenant.logout') }}" method="POST" class="d-none">
                                    @csrf
                                </form>
                            </div>
                        </li>
                    @endguest
                </ul>
            </div>
        </div>
    </nav>

    <!-- Intro modal -->
    @if($should_show_wizard_tutorial ?? false)
        <div class="modal fade" id="wizardTutorialModal" tabindex="-1" role="dialog" aria-labelledby="wizardTutorialModalTitle"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="wizardTutorialLongTitle">Upute za ispunjavanje obrasca</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                       <p>
                           Odabrali ste obrazac za izradu dokumenta: <strong>{{ $model->document->template->public_title ?? '' }}.</strong>
                       </p>
                        <p>
                            Obrazac se sastoji od nekoliko koraka koje trebate ispuniti. Možete se kretati između koraka
                            koristeći navigaciju na vrhu stranice.
                        </p>
                        <p>
                            Na desnoj strani stranice možete vidjeti pregled dokumenta koji se automatski ažurira dok
                            unosite podatke.
                        </p>
                        <p>
                            Kada završite s unosom podataka u trenutnom koraku, kliknite na gumb "Spremi i nastavi" za
                            prelazak na sljedeći korak.
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">Razumijem</button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Save changes modal -->
    <div class="modal fade" id="saveChangesModal" tabindex="-1" role="dialog" aria-labelledby="saveChangesModalTitle"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="saveChangesModalTitle">Nespremljene promjene</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Imate nespremljene promjene. Želite li ih spremiti prije prelaska na sljedeći korak?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="continue-without-saving">Nastavi bez spremanja</button>
                    <button type="button" class="btn btn-primary" id="save-and-continue">Spremi i nastavi</button>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="intended-exit-page" value="">

    <div id="layout-content">
        <div class="container-fluid margin-top">
            @if (session('success'))
                <div class="alert alert-success">
                    {!! session('success') !!}
                </div>
            @endif
            @if (session('error'))
                <div class="alert alert-danger">
                    {!! session('error') !!}
                </div>
            @endif
            @if (isset($errors) && $errors->any())
                <div class="alert alert-danger">
                    @if($errors->has('override'))
                        {{ collect($errors->get('override'))->first() }}
                    @else
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                </div>
            @endif
            <div class="row">
                <div class="col-lg-12">
                    <h1>
                        {{ $model->document->template->public_title ?? '' }}
                        <span class="mobile-content"><small> - {{ $model->section->title ?? '' }}</small></span>
                    </h1>
                </div>

                <div id="nav-container" class="col-lg-12 section-navbar-sticky pt-3 pb-2">
                    <div class="card" style="border-radius:0;">
                        <div class="card-header p-0">
                            <nav class="p-0 navbar navbar-expand-sm navbar-light bg-light">
                                <div class="collapse navbar-collapse" id="sectionNavbar">
                                    <ul class="navbar-nav mr-auto text-center">
                                        @if(isset($model) && $model->document && $model->document->template)
                                            @foreach($model->document->template->sections as $_section)
                                                <li style="width:{!! 100/count($model->document->template->sections) !!}%;" class="nav-item {!! $_section->id == $model->section->id ? 'active' : '' !!}">
                                                    <a class="nav-link" href="{!! route('tenant.section.show', [$model->document, $_section]) !!}">
                                                        {!! $_section->nav_title !!}
                                                        @php
                                                            $section_values = $model->document->sectionValues->where('document_template_section_id', $_section->id)->first();
                                                            $missing_data = $section_values ? json_decode($section_values->missing_data, true) : null;
                                                        @endphp
                                                        @if(!empty($missing_data))
                                                            <i class="fa fa-exclamation-triangle text-warning ml-1" title="Nedostaju podaci"></i>
                                                        @elseif($section_values)
                                                            <i class="fa fa-check text-success ml-1" title="Završeno"></i>
                                                        @endif
                                                    </a>
                                                </li>
                                            @endforeach
                                        @endif
                                    </ul>
                                </div>
                            </nav>
                        </div>
                        @if(isset($model) && $model->document && $model->document->template)
                            <div class="progress" style="border-radius:0;">
                                <div class="progress-bar bg-info" role="progressbar" style="width: {{ (int)($model->section->order_index / count($model->document->template->sections) * 100) }}%;" aria-valuenow="{{ (int)($model->section->order_index / count($model->document->template->sections) * 100) }}" aria-valuemin="0" aria-valuemax="100">{{ (int)($model->section->order_index / count($model->document->template->sections) * 100) }}%</div>
                            </div>
                        @endif
                    </div>
                </div>
                <div id="javascript-error-container" class="col-lg-12 pt-2 d-none">
                    <div id="javascript-error" class="alert alert-danger">

                    </div>
                </div>
                <div id="section-content" data-preview="true" class="col-lg-12" style="display: none;">
                    <div class="row">
                        <div id="form-container" data-missing="{{ !empty($model->missing_data ?? []) ? json_encode($model->missing_data) : "[]" }}" class="col-lg-6">
                            @yield('content')
                            @include('tenant.documents.partials.form_footer')
                        </div>
                        <div id="preview-sidebar" class="col-lg-6 pl-lg-2" style="margin-bottom: 1rem">
                            @include('tenant.documents.partials.preview_sidebar')
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; {{ date('Y') }} {{ config('app.name') }}. Sva prava pridržana.</p>
                </div>
                <div class="col-md-6 text-md-right">
                    <p class="mb-0">Powered by {{ config('app.name') }}</p>
                </div>
            </div>
        </div>
    </footer>
</div>

{!! GoogleReCaptchaV3::init() !!}

<!-- Include section-specific JavaScript -->
@if(isset($js) && !empty($js))
    <script src="{{ $js }}"></script>
@endif

<!-- Include common wizard JavaScript -->
<script src="{{ mix('js/tenant-wizard.js') }}"></script>

@stack('scripts')
</body>
</html>
