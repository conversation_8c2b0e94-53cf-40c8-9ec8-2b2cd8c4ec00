/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},d:(t,i)=>{for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{MediaUpload:()=>m,uploadMedia:()=>y});const i=window.wp.element,o=window.wp.i18n,a=[],s=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Select.extend({featuredImageToolbar(t){this.createSelectToolbar(t,{text:e.media.view.l10n.setFeaturedImage,state:this.options.state})},editState(){const t=this.state("featured-image").get("selection"),i=new e.media.view.EditImage({model:t.single(),controller:this}).render();this.content.set(i),i.loadEditor()},createStates:function(){this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new e.media.controller.FeaturedImage,new e.media.controller.EditImage({model:this.options.editImage})])}})},l=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Post.extend({galleryToolbar(){const t=this.state().get("editing");this.toolbar.set(new e.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:t?e.media.view.l10n.updateGallery:e.media.view.l10n.insertGallery,priority:80,requires:{library:!0},click(){const e=this.controller,t=e.state();e.close(),t.trigger("update",t.get("library")),e.setState(e.options.state),e.reset()}}}}))},editState(){const t=this.state("gallery").get("selection"),i=new e.media.view.EditImage({model:t.single(),controller:this}).render();this.content.set(i),i.loadEditor()},createStates:function(){this.on("toolbar:create:main-gallery",this.galleryToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new e.media.controller.Library({id:"gallery",title:e.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:e.media.query({type:"image",...this.options.library})}),new e.media.controller.EditImage({model:this.options.editImage}),new e.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new e.media.controller.GalleryAdd])}})},r=e=>["sizes","mime","type","subtype","id","url","alt","link","caption"].reduce(((t,i)=>(e?.hasOwnProperty(i)&&(t[i]=e[i]),t)),{}),n=e=>{const{wp:t}=window;return t.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"})};class d extends i.Component{constructor(){super(...arguments),this.openModal=this.openModal.bind(this),this.onOpen=this.onOpen.bind(this),this.onSelect=this.onSelect.bind(this),this.onUpdate=this.onUpdate.bind(this),this.onClose=this.onClose.bind(this)}initializeListeners(){this.frame.on("select",this.onSelect),this.frame.on("update",this.onUpdate),this.frame.on("open",this.onOpen),this.frame.on("close",this.onClose)}buildAndSetGalleryFrame(){const{addToGallery:e=!1,allowedTypes:t,multiple:i=!1,value:o=a}=this.props;if(o===this.lastGalleryValue)return;const{wp:s}=window;let r;this.lastGalleryValue=o,this.frame&&this.frame.remove(),r=e?"gallery-library":o&&o.length?"gallery-edit":"gallery",this.GalleryDetailsMediaFrame||(this.GalleryDetailsMediaFrame=l());const d=n(o),m=new s.media.model.Selection(d.models,{props:d.props.toJSON(),multiple:i});this.frame=new this.GalleryDetailsMediaFrame({mimeType:t,state:r,multiple:i,selection:m,editing:!(!o||!o.length)}),s.media.frame=this.frame,this.initializeListeners()}buildAndSetFeatureImageFrame(){const{wp:e}=window,{value:t,multiple:i,allowedTypes:o}=this.props,a=s(),l=n(t),r=new e.media.model.Selection(l.models,{props:l.props.toJSON()});this.frame=new a({mimeType:o,state:"featured-image",multiple:i,selection:r,editing:t}),e.media.frame=this.frame,e.media.view.settings.post={...e.media.view.settings.post,featuredImageId:t||-1}}componentWillUnmount(){this.frame?.remove()}onUpdate(e){const{onSelect:t,multiple:i=!1}=this.props,o=this.frame.state(),a=e||o.get("selection");a&&a.models.length&&t(i?a.models.map((e=>r(e.toJSON()))):r(a.models[0].toJSON()))}onSelect(){const{onSelect:e,multiple:t=!1}=this.props,i=this.frame.state().get("selection").toJSON();e(t?i:i[0])}onOpen(){const{wp:e}=window,{value:t}=this.props;this.updateCollection(),this.props.mode&&this.frame.content.mode(this.props.mode);if(!(Array.isArray(t)?!!t?.length:!!t))return;const i=this.props.gallery,o=this.frame.state().get("selection"),a=Array.isArray(t)?t:[t];i||a.forEach((t=>{o.add(e.media.attachment(t))}));const s=n(a);s.more().done((function(){i&&s?.models?.length&&o.add(s.models)}))}onClose(){const{onClose:e}=this.props;e&&e()}updateCollection(){const e=this.frame.content.get();if(e&&e.collection){const t=e.collection;t.toArray().forEach((e=>e.trigger("destroy",e))),t.mirroring._hasMore=!0,t.more()}}openModal(){const{allowedTypes:e,gallery:t=!1,unstableFeaturedImageFlow:i=!1,modalClass:a,multiple:s=!1,title:l=(0,o.__)("Select or Upload Media")}=this.props,{wp:r}=window;if(t)this.buildAndSetGalleryFrame();else{const t={title:l,multiple:s};e&&(t.library={type:e}),this.frame=r.media(t)}a&&this.frame.$el.addClass(a),i&&this.buildAndSetFeatureImageFrame(),this.initializeListeners(),this.frame.open()}render(){return this.props.render({open:this.openModal})}}const m=d,p=window.wp.apiFetch;var c=e.n(p);const h=window.wp.blob,u=()=>{};async function y({allowedTypes:e,additionalData:t={},filesList:i,maxUploadFileSize:a,onError:s=u,onFileChange:l,wpAllowedMimeTypes:r=null}){const n=[...i],d=[],m=(e,t)=>{(0,h.revokeBlobURL)(d[e]?.url),d[e]=t,l(d.filter(Boolean))},p=t=>!e||e.some((e=>e.includes("/")?e===t:t.startsWith(`${e}/`))),c=(y=r)?Object.entries(y).map((([e,t])=>{const[i]=t.split("/");return[t,...e.split("|").map((e=>`${i}/${e}`))]})).flat():y;var y;const g=[];for(const e of n)c&&e.type&&(f=e.type,!c.includes(f))?s({code:"MIME_TYPE_NOT_ALLOWED_FOR_USER",message:(0,o.sprintf)((0,o.__)("%s: Sorry, you are not allowed to upload this file type."),e.name),file:e}):!e.type||p(e.type)?a&&e.size>a?s({code:"SIZE_ABOVE_LIMIT",message:(0,o.sprintf)((0,o.__)("%s: This file exceeds the maximum upload size for this site."),e.name),file:e}):e.size<=0?s({code:"EMPTY_FILE",message:(0,o.sprintf)((0,o.__)("%s: This file is empty."),e.name),file:e}):(g.push(e),d.push({url:(0,h.createBlobURL)(e)}),l(d)):s({code:"MIME_TYPE_NOT_SUPPORTED",message:(0,o.sprintf)((0,o.__)("%s: Sorry, this file type is not supported here."),e.name),file:e});var f;for(let e=0;e<g.length;++e){const i=g[e];try{var b;const o=await w(i,t),{alt_text:a,source_url:s,...l}=o;m(e,{...l,alt:o.alt_text,caption:null!==(b=o.caption?.raw)&&void 0!==b?b:"",title:o.title.raw,url:o.source_url})}catch(t){let a;m(e,null),a=t.message?t.message:(0,o.sprintf)((0,o.__)("Error while uploading file %s to the media library."),i.name),s({code:"GENERAL",message:a,file:i})}}}function w(e,t){const i=new window.FormData;return i.append("file",e,e.name||e.type.replace("/",".")),t&&Object.entries(t).forEach((([e,t])=>i.append(e,t))),c()({path:"/wp/v2/media",body:i,method:"POST"})}(window.wp=window.wp||{}).mediaUtils=t})();