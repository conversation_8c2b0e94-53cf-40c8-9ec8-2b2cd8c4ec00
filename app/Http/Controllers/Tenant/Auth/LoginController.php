<?php

namespace App\Http\Controllers\Tenant\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Mail\OtpMail;
use App\Models\User;
use Carbon\Carbon;
use App\Rules\GoogleReCaptchaValidationRule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
	use AuthenticatesUsers;

	/**
	 * Where to redirect users after login.
	 *
	 */
	// protected $redirectTo = '/moji-dokumenti';

	// handle redirection manually after OTP verification

	/**
	 * Create a new controller instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		$this->middleware('guest')->except(['logout']);
	}

	/**
	 * Show the application's login form.
	 *
	 * @return \Illuminate\View\View
	 */
	public function showLoginForm()
	{
		return view('tenant.auth.login');
	}

	/**
	 * Validate the user login request.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return void
	 *
	 * @throws \Illuminate\Validation\ValidationException
	 */
	protected function validateLogin(Request $request)
	{
		$validate_params = [
			$this->username() => 'required|string|email',
			'password' => 'required|string',
			'g-recaptcha-response' => [new GoogleReCaptchaValidationRule('tenant_login')]
		];

		Validator::make($request->all(), $validate_params)->validate();
	}

	/**
	 * The user has been authenticated.
	 * This method is called after password validation passes.
	 * We override it to implement the OTP step.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  mixed  $user
	 * @return \Illuminate\Http\RedirectResponse
	 */
	protected function authenticated(Request $request, $user)
	{
		// Skip OTP if disabled in environment
		if (config('tenancy.tenant.otp_enabled') === false) {
			return redirect()->intended(route('tenant.documents'));
		}

		// generate OTP
		$otp = random_int(100000, 999999); // Generate a 6-digit OTP
		$expires_at = Carbon::now()->addMinutes(config('tenancy.tenant.otp_expiration'));

		// store OTP and expiration in the user record
		$user->otp_code = $otp;
		$user->otp_expires_at = $expires_at;
		$user->save();

		// send OTP via Email
		try {
			Mail::to($user->email)->send(new OtpMail((string)$otp));
		} catch (\Exception $e) {
			// Log error or handle mail sending failure
			report($e);
			// Log the user out and redirect back with an error
			$this->guard()->logout();
			$request->session()->invalidate();
			$request->session()->regenerateToken();
			return redirect()->route('tenant.login')
			                 ->withErrors(['email' => 'Došlo je do pogreške prilikom slanja koda. Molimo pokušajte ponovno.']); // Error sending code
		}

		// store user ID in session to know who is verifying OTP
		$request->session()->put('otp_user_id', $user->id);

		// log the user out temporarily until OTP is verified
		$this->guard()->logout();

		// redirect to OTP verification form
		return redirect()->route('tenant.otp.form');
	}

	/**
	 * Show the OTP verification form.
	 *
	 * @param Request $request
	 * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
	 */
	public function showOtpForm(Request $request)
	{
		// if session doesn't have the user ID, redirect to login
		if (!$request->session()->has('otp_user_id')) {
			return redirect()->route('tenant.login');
		}

		return view('tenant.auth.otp-verify');
	}

	/**
	 * Verify the submitted OTP.
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\RedirectResponse
	 */
	public function verifyOtp(Request $request)
	{
		$request->validate([
			'otp' => 'required|numeric|digits:6',
		]);

		$user_id = $request->session()->get('otp_user_id');

		// session expired
		if (!$user_id) {
			return redirect()->route('tenant.login')->withErrors(['otp' => 'Sesija je istekla, molimo prijavite se ponovno.']);
		}

		$user = User::find($user_id);

		if (!$user) {
			return redirect()->route('tenant.login')->withErrors(['otp' => 'Korisnik nije pronađen.']); // User not found
		}

		// check if OTP matches and hasn't expired
		if ($user->otp_code === $request->otp && $user->otp_expires_at && Carbon::now()->isBefore($user->otp_expires_at)) {
			// OTP is valid

			// clear OTP fields
			$user->otp_code = null;
			$user->otp_expires_at = null;
			$user->save();

			// log the user in permanently
			Auth::guard($this->getGuard())->login($user, $request->filled('remember'));

			// clear the session variable
			$request->session()->forget('otp_user_id');
			$request->session()->regenerate(); // regenerate session ID after successful login

			// redirect to the intended destination or documents
			return redirect()->intended(route('tenant.documents'));
		} else {
			// OTP is invalid or expired, redirect back with error
			return redirect()->back()->withErrors(['otp' => 'Kod je nevaljan ili je istekao.']);
		}
	}

	/**
	 * Log the user out of the application.
	 */
	public function logout(Request $request)
	{
		// clear OTP session data on logout as well
		$request->session()->forget('otp_user_id');

		$this->guard()->logout();

		$request->session()->invalidate();

		$request->session()->regenerateToken();

		return $request->wantsJson()
			? new JsonResponse([], 204)
			: redirect()->route('tenant.login');
	}

	// helper to get the guard name
	protected function getGuard()
	{
		return property_exists($this, 'guard') ? $this->guard : null;
	}
}
