let mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix
    .js('resources/assets/js/app.js', 'public/js')
    .js('resources/assets/js/tenant-wizard.js', 'public/js')

    // editor js files
    .js('resources/assets/js/editor/parties.js', 'public/js/editor')
    .js('resources/assets/js/editor/title.js', 'public/js/editor')
    .js('resources/assets/js/editor/body.js', 'public/js/editor')
    .js('resources/assets/js/editor/article_header.js', 'public/js/editor')
    .js('resources/assets/js/editor/article_body.js', 'public/js/editor')
    .js('resources/assets/js/editor/signees.js', 'public/js/editor')
    .js('resources/assets/js/editor/attachments.js', 'public/js/editor')
    .js('resources/assets/js/editor/legal_remedy.js', 'public/js/editor')
    .js('resources/assets/js/editor/recipients.js', 'public/js/editor')
    .js('resources/assets/js/editor/editor.js', 'public/js/editor')
    .js('resources/assets/js/editor/spellcheck.js', 'public/js/editor')

    // document js files
    .js('resources/assets/js/documents/common.js', 'public/js/documents/')
    .js('resources/assets/js/documents/AuthorServiceContract/additional_provisions.js', 'public/js/documents/AuthorServiceContract/')
    .js('resources/assets/js/documents/AuthorServiceContract/final_provisions.js', 'public/js/documents/AuthorServiceContract/')
    .js('resources/assets/js/documents/AuthorServiceContract/financial_information.js', 'public/js/documents/AuthorServiceContract/')
    .js('resources/assets/js/documents/AuthorServiceContract/parties.js', 'public/js/documents/AuthorServiceContract/')
    .js('resources/assets/js/documents/AuthorServiceContract/service.js', 'public/js/documents/AuthorServiceContract/')
    .js('resources/assets/js/documents/MotorVehicleGiftAgreement/final_provisions.js', 'public/js/documents/MotorVehicleGiftAgreement/')
    .js('resources/assets/js/documents/MotorVehicleGiftAgreement/financial_information.js', 'public/js/documents/MotorVehicleGiftAgreement/')
    .js('resources/assets/js/documents/MotorVehicleGiftAgreement/ownership_transfer.js', 'public/js/documents/MotorVehicleGiftAgreement/')
    .js('resources/assets/js/documents/MotorVehicleGiftAgreement/parties.js', 'public/js/documents/MotorVehicleGiftAgreement/')
    .js('resources/assets/js/documents/MotorVehicleGiftAgreement/vehicle_information.js', 'public/js/documents/MotorVehicleGiftAgreement/')
    .js('resources/assets/js/documents/MotorVehicleSalesAgreement/final_provisions.js', 'public/js/documents/MotorVehicleSalesAgreement/')
    .js('resources/assets/js/documents/MotorVehicleSalesAgreement/financial_information.js', 'public/js/documents/MotorVehicleSalesAgreement/')
    .js('resources/assets/js/documents/MotorVehicleSalesAgreement/ownership_transfer.js', 'public/js/documents/MotorVehicleSalesAgreement/')
    .js('resources/assets/js/documents/MotorVehicleSalesAgreement/parties.js', 'public/js/documents/MotorVehicleSalesAgreement/')
    .js('resources/assets/js/documents/MotorVehicleSalesAgreement/vehicle_information.js', 'public/js/documents/MotorVehicleSalesAgreement/')
    .js('resources/assets/js/documents/PropertyRentalAgreement/final_provisions.js', 'public/js/documents/PropertyRentalAgreement/')
    .js('resources/assets/js/documents/PropertyRentalAgreement/parties.js', 'public/js/documents/PropertyRentalAgreement/')
    .js('resources/assets/js/documents/PropertyRentalAgreement/property.js', 'public/js/documents/PropertyRentalAgreement/')
    .js('resources/assets/js/documents/PropertyRentalAgreement/rent.js', 'public/js/documents/PropertyRentalAgreement/')
    .js('resources/assets/js/documents/PropertyRentalAgreement/rules.js', 'public/js/documents/PropertyRentalAgreement/')
    .js('resources/assets/js/documents/RealestateGiftContract/final_provisions.js', 'public/js/documents/RealestateGiftContract/')
    .js('resources/assets/js/documents/RealestateGiftContract/financial_information.js', 'public/js/documents/RealestateGiftContract/')
    .js('resources/assets/js/documents/RealestateGiftContract/ownership_transfer.js', 'public/js/documents/RealestateGiftContract/')
    .js('resources/assets/js/documents/RealestateGiftContract/parties.js', 'public/js/documents/RealestateGiftContract/')
    .js('resources/assets/js/documents/RealestateGiftContract/realestate.js', 'public/js/documents/RealestateGiftContract/')
    .js('resources/assets/js/documents/RealestateSalesPrecontract/conditions.js', 'public/js/documents/RealestateSalesPrecontract/')
    .js('resources/assets/js/documents/RealestateSalesPrecontract/final_provisions.js', 'public/js/documents/RealestateSalesPrecontract/')
    .js('resources/assets/js/documents/RealestateSalesPrecontract/financial_information.js', 'public/js/documents/RealestateSalesPrecontract/')
    .js('resources/assets/js/documents/RealestateSalesPrecontract/ownership_transfer.js', 'public/js/documents/RealestateSalesPrecontract/')
    .js('resources/assets/js/documents/RealestateSalesPrecontract/parties.js', 'public/js/documents/RealestateSalesPrecontract/')
    .js('resources/assets/js/documents/RealestateSalesPrecontract/realestate.js', 'public/js/documents/RealestateSalesPrecontract/')
    .js('resources/assets/js/documents/ServiceContract/additional_provisions.js', 'public/js/documents/ServiceContract/')
    .js('resources/assets/js/documents/ServiceContract/final_provisions.js', 'public/js/documents/ServiceContract/')
    .js('resources/assets/js/documents/ServiceContract/financial_information.js', 'public/js/documents/ServiceContract/')
    .js('resources/assets/js/documents/ServiceContract/parties.js', 'public/js/documents/ServiceContract/')
    .js('resources/assets/js/documents/ServiceContract/service.js', 'public/js/documents/ServiceContract/')
    .js('resources/assets/js/documents/WorkContract/additional_provisions.js', 'public/js/documents/WorkContract/')
    .js('resources/assets/js/documents/WorkContract/final_provisions.js', 'public/js/documents/WorkContract/')
    .js('resources/assets/js/documents/WorkContract/general_provisions.js', 'public/js/documents/WorkContract/')
    .js('resources/assets/js/documents/WorkContract/parties.js', 'public/js/documents/WorkContract/')
    .js('resources/assets/js/documents/WorkContract/type.js', 'public/js/documents/WorkContract/')
    .js('resources/assets/js/documents/WorkContractRemote/additional_provisions.js', 'public/js/documents/WorkContractRemote/')
    .js('resources/assets/js/documents/WorkContractRemote/final_provisions.js', 'public/js/documents/WorkContractRemote/')
    .js('resources/assets/js/documents/WorkContractRemote/general_provisions.js', 'public/js/documents/WorkContractRemote/')
    .js('resources/assets/js/documents/WorkContractRemote/parties.js', 'public/js/documents/WorkContractRemote/')
    .js('resources/assets/js/documents/WorkContractRemote/type.js', 'public/js/documents/WorkContractRemote/')
	.js('resources/assets/js/documents/RealestateSalesPOA/parties.js', 'public/js/documents/RealestateSalesPOA/')
	.js('resources/assets/js/documents/RealestateSalesPOA/realestate.js', 'public/js/documents/RealestateSalesPOA/')
	.js('resources/assets/js/documents/RealestateSalesPOA/authorities.js', 'public/js/documents/RealestateSalesPOA/')
	.js('resources/assets/js/documents/RealestateSalesPOA/validity.js', 'public/js/documents/RealestateSalesPOA/')
	.js('resources/assets/js/documents/RealestateSalesPOA/final_provisions.js', 'public/js/documents/RealestateSalesPOA/')
	.js('resources/assets/js/documents/RealestatePurchasePOA/parties.js', 'public/js/documents/RealestatePurchasePOA/')
	.js('resources/assets/js/documents/RealestatePurchasePOA/realestate.js', 'public/js/documents/RealestatePurchasePOA/')
	.js('resources/assets/js/documents/RealestatePurchasePOA/authorities.js', 'public/js/documents/RealestatePurchasePOA/')
	.js('resources/assets/js/documents/RealestatePurchasePOA/validity.js', 'public/js/documents/RealestatePurchasePOA/')
	.js('resources/assets/js/documents/RealestatePurchasePOA/final_provisions.js', 'public/js/documents/RealestatePurchasePOA/')
	.js('resources/assets/js/documents/MatrimonialRealestateTransferConsent/parties.js', 'public/js/documents/MatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/MatrimonialRealestateTransferConsent/realestate.js', 'public/js/documents/MatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/MatrimonialRealestateTransferConsent/authorities.js', 'public/js/documents/MatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/MatrimonialRealestateTransferConsent/validity.js', 'public/js/documents/MatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/MatrimonialRealestateTransferConsent/final_provisions.js', 'public/js/documents/MatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/NonMatrimonialRealestateTransferConsent/parties.js', 'public/js/documents/NonMatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/NonMatrimonialRealestateTransferConsent/realestate.js', 'public/js/documents/NonMatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/documents/NonMatrimonialRealestateTransferConsent/final_provisions.js', 'public/js/documents/NonMatrimonialRealestateTransferConsent/')
	.js('resources/assets/js/tenant/documents/client/WorkContract/parties.js', 'public/js/tenant/documents/client/WorkContract/')
	.js('resources/assets/js/tenant/documents/client/WorkContract/service.js', 'public/js/tenant/documents/client/WorkContract/')
	.js('resources/assets/js/tenant/documents/client/WorkContract/final_provisions.js', 'public/js/tenant/documents/client/WorkContract/')
	//{addEntryPlaceholder}

	// translation js files
	.js('resources/assets/js/translations/checkout.js', 'public/js/translations/')
	.js('resources/assets/js/translations/custom_checkout.js', 'public/js/translations/')
	.js('resources/assets/js/translations/edit.js', 'public/js/translations/')

    .css('resources/assets/css/app.css', 'public/css')
    .css('resources/assets/css/preview.css', 'public/css')
    .css('resources/assets/css/editor.css', 'public/css')
    .css('resources/assets/css/translations.css', 'public/css')
    .css('resources/assets/css/tenant.css', 'public/css')
    .version();
